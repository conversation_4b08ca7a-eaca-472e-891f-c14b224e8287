/**
 * Mermaid Integration Module
 * Handles Mermaid diagram rendering and integration
 */

class MermaidIntegration {
  constructor() {
    this.isInitialized = false;
    this.diagramCounter = 0;
    this.config = {
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      fontFamily: 'inherit',
      fontSize: 14,
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true
      },
      sequence: {
        useMaxWidth: true,
        wrap: true
      },
      gantt: {
        useMaxWidth: true
      }
    };
    
    this.initialize();
  }

  /**
   * Initialize Mermaid
   */
  initialize() {
    if (typeof mermaid === 'undefined') {
      console.warn('Mermaid library not loaded');
      return;
    }

    try {
      // Enhanced configuration for better compatibility
      const enhancedConfig = {
        ...this.config,
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        deterministicIds: false,
        deterministicIDSeed: undefined
      };

      mermaid.initialize(enhancedConfig);
      this.isInitialized = true;
      console.log('Mermaid initialized successfully with version:', mermaid.version || 'unknown');
    } catch (error) {
      console.error('Failed to initialize Mermaid:', error);
    }
  }

  /**
   * Check if Mermaid is available
   * @returns {boolean}
   */
  isAvailable() {
    return typeof mermaid !== 'undefined' && this.isInitialized;
  }

  /**
   * Process HTML content and render Mermaid diagrams
   * @param {string} html - HTML content to process
   * @returns {Promise<string>} Processed HTML with rendered diagrams
   */
  async processHtml(html) {
    if (!this.isAvailable()) {
      console.warn('Mermaid not available for processing');
      return html;
    }

    try {
      // Find all code blocks with mermaid language
      const mermaidRegex = /<pre><code class="language-mermaid">([\s\S]*?)<\/code><\/pre>/g;
      let processedHtml = html;
      const matches = [];
      let match;

      // Collect all matches first
      while ((match = mermaidRegex.exec(html)) !== null) {
        matches.push({
          fullMatch: match[0],
          diagramCode: this.decodeHtmlEntities(match[1].trim()),
          index: match.index
        });
      }

      console.log(`Found ${matches.length} Mermaid diagrams to process`);

      // Process matches in reverse order to maintain indices
      for (let i = matches.length - 1; i >= 0; i--) {
        const matchData = matches[i];
        const diagramId = `mermaid-diagram-${++this.diagramCounter}`;
        
        try {
          // Validate the diagram syntax
          await mermaid.parse(matchData.diagramCode);
          
          // Create container for the diagram
          const diagramContainer = `
            <div class="mermaid-container" id="${diagramId}-container">
              <div class="mermaid-diagram" id="${diagramId}">${matchData.diagramCode}</div>
              <div class="mermaid-source" style="display: none;">
                <pre><code class="language-mermaid">${this.escapeHtml(matchData.diagramCode)}</code></pre>
              </div>
              <div class="mermaid-controls">
                <button class="mermaid-toggle-source" onclick="window.mermaidIntegration.toggleSource('${diagramId}')">
                  <i class="mdi mdi-code-tags"></i> Show Source
                </button>
                <button class="mermaid-copy" onclick="window.mermaidIntegration.copyDiagram('${diagramId}')">
                  <i class="mdi mdi-content-copy"></i> Copy
                </button>
              </div>
            </div>
          `;
          
          processedHtml = processedHtml.replace(matchData.fullMatch, diagramContainer);
        } catch (parseError) {
          console.warn('Invalid Mermaid syntax:', parseError);
          // Keep original code block but add error indicator
          const errorBlock = `
            <div class="mermaid-error">
              <div class="error-message">
                <i class="mdi mdi-alert-circle"></i>
                Invalid Mermaid diagram syntax
              </div>
              <pre><code class="language-mermaid">${this.escapeHtml(matchData.diagramCode)}</code></pre>
            </div>
          `;
          processedHtml = processedHtml.replace(matchData.fullMatch, errorBlock);
        }
      }

      return processedHtml;
    } catch (error) {
      console.error('Error processing Mermaid diagrams:', error);
      return html;
    }
  }

  /**
   * Render Mermaid diagrams in the given element
   * @param {HTMLElement} element - Element containing Mermaid diagrams
   */
  async renderDiagrams(element) {
    if (!this.isAvailable()) {
      return;
    }

    try {
      const mermaidElements = element.querySelectorAll('.mermaid-diagram');
      console.log(`Found ${mermaidElements.length} Mermaid elements to render`);

      for (const mermaidElement of mermaidElements) {
        if (!mermaidElement.hasAttribute('data-processed')) {
          try {
            // Get the diagram code from the element
            const diagramCode = mermaidElement.textContent.trim();

            // Clear the element content
            mermaidElement.innerHTML = '';

            // Use the newer Mermaid API (v10+)
            if (typeof mermaid.render === 'function') {
              // Mermaid v10+ API
              const diagramId = `diagram-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
              const { svg } = await mermaid.render(diagramId, diagramCode);
              mermaidElement.innerHTML = svg;
            } else if (typeof mermaid.mermaidAPI !== 'undefined' && typeof mermaid.mermaidAPI.render === 'function') {
              // Mermaid v8-9 API
              console.log('Using Mermaid v8-9 mermaidAPI.render');
              const diagramId = `diagram-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
              await new Promise((resolve, reject) => {
                mermaid.mermaidAPI.render(diagramId, diagramCode, (svg) => {
                  mermaidElement.innerHTML = svg;
                  console.log('Mermaid diagram rendered successfully with v8-9 API');
                  resolve();
                }, (error) => {
                  reject(error);
                });
              });
            } else if (typeof mermaid.run === 'function') {
              // Mermaid v9 run API
              mermaidElement.textContent = diagramCode;
              await mermaid.run({
                nodes: [mermaidElement]
              });
            } else {
              // Fallback for older versions
              mermaidElement.textContent = diagramCode;
              mermaidElement.classList.add('mermaid');
              await mermaid.init(undefined, mermaidElement);
            }

            mermaidElement.setAttribute('data-processed', 'true');
          } catch (renderError) {
            console.warn('Error rendering Mermaid diagram:', renderError);
            mermaidElement.innerHTML = `
              <div class="mermaid-render-error">
                <i class="mdi mdi-alert-circle"></i>
                Error rendering diagram: ${renderError.message}
              </div>
            `;
          }
        }
      }
    } catch (error) {
      console.error('Error in renderDiagrams:', error);
    }
  }

  /**
   * Toggle source code visibility for a diagram
   * @param {string} diagramId - ID of the diagram
   */
  toggleSource(diagramId) {
    const container = document.getElementById(`${diagramId}-container`);
    if (!container) return;

    const diagram = container.querySelector('.mermaid-diagram');
    const source = container.querySelector('.mermaid-source');
    const button = container.querySelector('.mermaid-toggle-source');

    if (source.style.display === 'none') {
      diagram.style.display = 'none';
      source.style.display = 'block';
      button.innerHTML = '<i class="mdi mdi-chart-timeline-variant"></i> Show Diagram';
    } else {
      diagram.style.display = 'block';
      source.style.display = 'none';
      button.innerHTML = '<i class="mdi mdi-code-tags"></i> Show Source';
    }
  }

  /**
   * Copy diagram source to clipboard
   * @param {string} diagramId - ID of the diagram
   */
  async copyDiagram(diagramId) {
    const container = document.getElementById(`${diagramId}-container`);
    if (!container) return;

    const source = container.querySelector('.mermaid-source code');
    if (source) {
      try {
        await navigator.clipboard.writeText(source.textContent);
        
        // Show feedback
        const button = container.querySelector('.mermaid-copy');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="mdi mdi-check"></i> Copied!';
        setTimeout(() => {
          button.innerHTML = originalText;
        }, 2000);
      } catch (error) {
        console.error('Failed to copy to clipboard:', error);
      }
    }
  }

  /**
   * Get diagram templates
   * @returns {Array} Array of diagram templates
   */
  getTemplates() {
    return [
      {
        name: 'Flowchart',
        code: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E`
      },
      {
        name: 'Sequence Diagram',
        code: `sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello Bob, how are you?
    B-->>A: Great!`
      },
      {
        name: 'Class Diagram',
        code: `classDiagram
    class Animal {
        +String name
        +int age
        +makeSound()
    }
    class Dog {
        +String breed
        +bark()
    }
    Animal <|-- Dog`
      },
      {
        name: 'State Diagram',
        code: `stateDiagram-v2
    [*] --> Still
    Still --> [*]
    Still --> Moving
    Moving --> Still
    Moving --> Crash
    Crash --> [*]`
      },
      {
        name: 'Pie Chart',
        code: `pie title Pets adopted by volunteers
    "Dogs" : 386
    "Cats" : 85
    "Rats" : 15`
      }
    ];
  }

  /**
   * Insert a diagram template
   * @param {string} templateName - Name of the template
   */
  insertTemplate(templateName) {
    const templates = this.getTemplates();
    const template = templates.find(t => t.name === templateName);

    if (template && window.editor) {
      const diagramCode = `\`\`\`mermaid\n${template.code}\n\`\`\`\n\n`;

      // Use the editor's insertText method if available, otherwise use EditorUtils
      if (typeof window.editor.insertText === 'function') {
        window.editor.insertText(diagramCode);
      } else if (window.EditorUtils && window.editor.editorElement) {
        window.EditorUtils.insertTextAtCursor(window.editor.editorElement, diagramCode);
        window.editor.handleInput();
      }

      window.editor.focus();
    }
  }

  /**
   * Decode HTML entities
   * @param {string} html - HTML string with entities
   * @returns {string} Decoded string
   */
  decodeHtmlEntities(html) {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = html;
    return textarea.value;
  }

  /**
   * Escape HTML characters
   * @param {string} text - Text to escape
   * @returns {string} Escaped text
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Initialize and expose globally
window.mermaidIntegration = new MermaidIntegration();
