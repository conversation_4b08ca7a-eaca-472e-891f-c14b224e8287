/**
 * Editor Module
 * Handles the main editor functionality and content management
 */

class Editor {
  constructor() {
    this.editorElement = null;
    this.previewElement = null;
    this.currentContent = '';
    this.currentFileName = 'Untitled.md';
    this.isModified = false;
    this.viewMode = 'split'; // 'edit', 'split', 'preview'
    this.previewEditMode = false;
    this.isUpdatingFromPreview = false;

    // Debounced functions for performance
    this.debouncedRender = EditorUtils.debounce(() => this.renderPreview(), 300);
    this.debouncedUpdateStats = EditorUtils.debounce(this.updateStatistics.bind(this), 500);
    this.debouncedSyncFromPreview = EditorUtils.debounce(this.syncFromPreview.bind(this), 500);

    this.initialize();
  }

  /**
   * Initialize the editor
   */
  initialize() {
    this.editorElement = document.getElementById('editor');
    this.previewElement = document.getElementById('preview');
    
    if (!this.editorElement || !this.previewElement) {
      console.error('Editor or preview element not found');
      return;
    }

    this.setupEventListeners();
    this.loadInitialContent();
    this.setupKeyboardShortcuts();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Editor input events
    this.editorElement.addEventListener('input', this.handleInput.bind(this));
    this.editorElement.addEventListener('paste', this.handlePaste.bind(this));
    this.editorElement.addEventListener('keydown', this.handleKeyDown.bind(this));
    this.editorElement.addEventListener('scroll', this.handleEditorScroll.bind(this));

    // Focus and blur events
    this.editorElement.addEventListener('focus', this.handleFocus.bind(this));
    this.editorElement.addEventListener('blur', this.handleBlur.bind(this));

    // Prevent default drag and drop on editor
    this.editorElement.addEventListener('dragover', (e) => e.preventDefault());
    this.editorElement.addEventListener('drop', this.handleDrop.bind(this));

    // Preview editing events
    this.setupPreviewEditing();
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    // Text formatting shortcuts
    EditorUtils.addKeyboardShortcut('ctrl+b', () => this.formatText('**', '**'));
    EditorUtils.addKeyboardShortcut('ctrl+i', () => this.formatText('*', '*'));
    EditorUtils.addKeyboardShortcut('ctrl+k', () => this.insertLink());
    EditorUtils.addKeyboardShortcut('ctrl+shift+k', () => this.insertImage());
    EditorUtils.addKeyboardShortcut('ctrl+m', () => this.insertEquation());
    EditorUtils.addKeyboardShortcut('ctrl+shift+m', () => this.insertEquation(true));

    // Editor shortcuts
    EditorUtils.addKeyboardShortcut('ctrl+/', () => this.toggleComment());
    EditorUtils.addKeyboardShortcut('tab', (e) => this.handleTab(e));
    EditorUtils.addKeyboardShortcut('shift+tab', (e) => this.handleShiftTab(e));

    // Preview shortcuts
    EditorUtils.addKeyboardShortcut('f11', (e) => {
      e.preventDefault();
      if (this.viewMode === 'preview') {
        this.togglePreviewExpanded();
      }
    });
    EditorUtils.addKeyboardShortcut('escape', (e) => {
      if (this.isPreviewExpanded) {
        e.preventDefault();
        this.setPreviewExpanded(false);
      }
    });

    // Prevent browser shortcuts that might interfere
    EditorUtils.addKeyboardShortcut('ctrl+s', (e) => {
      e.preventDefault();
      // File save will be handled by file manager
      window.fileManager?.saveFile();
    });
  }

  /**
   * Load initial content
   */
  loadInitialContent() {
    const initialContent = `# Welcome to Markdown Editor

This is a **modern** markdown editor with real-time preview, syntax highlighting, and **Mermaid diagram support**.

## Features

- 🚀 Real-time preview
- 🎨 Syntax highlighting
- 📁 File management
- 📤 Export options
- ⌨️ Keyboard shortcuts
- 📱 Responsive design
- 📊 **Mermaid diagrams**

## Getting Started

Start typing in the editor to see the magic happen! Use the toolbar buttons or keyboard shortcuts for quick formatting.

> **Tip:** Try pressing \`Ctrl+B\` to make text **bold** or \`Ctrl+I\` for *italic* text.

## Mermaid Diagrams

Create beautiful diagrams using Mermaid syntax:

### Flowchart Example

\`\`\`mermaid
graph TD
    A[Start Editing] --> B{Choose Format}
    B -->|Text| C[Write Content]
    B -->|Diagram| D[Create Mermaid]
    C --> E[Preview]
    D --> E[Preview]
    E --> F{Satisfied?}
    F -->|Yes| G[Save File]
    F -->|No| C
    G --> H[End]
\`\`\`

### Sequence Diagram

\`\`\`mermaid
sequenceDiagram
    participant User
    participant Editor
    participant Preview
    participant Mermaid

    User->>Editor: Types diagram code
    Editor->>Preview: Updates content
    Preview->>Mermaid: Process diagram
    Mermaid-->>Preview: Return SVG
    Preview-->>User: Display diagram
\`\`\`

### Class Diagram

\`\`\`mermaid
classDiagram
    class MarkdownEditor {
        +String content
        +Boolean isModified
        +renderPreview()
        +insertText()
        +saveFile()
    }

    class MermaidIntegration {
        +processHtml()
        +renderDiagrams()
        +getTemplates()
    }

    class FileManager {
        +openFile()
        +saveFile()
        +exportFile()
    }

    MarkdownEditor --> MermaidIntegration : uses
    MarkdownEditor --> FileManager : uses
\`\`\`

### Code Example

\`\`\`javascript
function greetUser(name) {
  console.log(\`Hello, \${name}! Welcome to the editor.\`);
}

greetUser('Developer');
\`\`\`

### Task List

- [x] Create awesome markdown editor
- [x] Add syntax highlighting
- [x] Add Mermaid diagram support
- [ ] Add more themes
- [ ] Add plugin system

### Table Example

| Feature | Status | Priority |
|---------|--------|----------|
| Editor | ✅ Complete | High |
| Preview | ✅ Complete | High |
| Mermaid | ✅ Complete | High |
| Export | ✅ Complete | Medium |
| Themes | 🚧 In Progress | Low |

### Math Equations

You can write inline math like $E = mc^2$ or display equations:

$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

More examples:
- Quadratic formula: $x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$
- Euler's identity: $e^{i\\pi} + 1 = 0$
- Matrix: $\\begin{pmatrix} a & b \\\\\\\\ c & d \\end{pmatrix}$

> **Tip:** Use \`Ctrl+M\` for inline math or \`Ctrl+Shift+M\` for display math!

---

Happy writing! 📝
`;

    this.setContent(initialContent);
    this.renderPreview().catch(console.error);
    this.updateStatistics();
  }

  /**
   * Handle input events
   */
  handleInput(event) {
    this.currentContent = this.editorElement.textContent || '';
    this.isModified = true;
    this.debouncedRender();
    this.debouncedUpdateStats();
    
    // Update document title to show modified state
    this.updateDocumentTitle();
  }

  /**
   * Handle paste events
   */
  handlePaste(event) {
    event.preventDefault();
    
    const clipboardData = event.clipboardData || window.clipboardData;
    const pastedText = clipboardData.getData('text/plain');
    
    // Insert the pasted text at cursor position
    EditorUtils.insertTextAtCursor(this.editorElement, pastedText);
    
    // Trigger input event manually
    this.handleInput();
  }

  /**
   * Handle keydown events
   */
  handleKeyDown(event) {
    // Handle tab key for indentation
    if (event.key === 'Tab') {
      event.preventDefault();
      this.handleTab(event);
      return;
    }
    
    // Handle enter key for smart line breaks
    if (event.key === 'Enter') {
      this.handleEnter(event);
    }
  }

  /**
   * Handle tab key for indentation
   */
  handleTab(event) {
    const isShift = event.shiftKey;
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);
    
    if (isShift) {
      // Remove indentation
      this.removeIndentation();
    } else {
      // Add indentation
      EditorUtils.insertTextAtCursor(this.editorElement, '  ');
    }
  }

  /**
   * Handle enter key for smart line breaks
   */
  handleEnter(event) {
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);
    const currentLine = this.getCurrentLine();
    
    // Check for list continuation
    const listMatch = currentLine.match(/^(\s*)([-*+]|\d+\.)\s/);
    if (listMatch) {
      event.preventDefault();
      const indent = listMatch[1];
      const marker = listMatch[2];
      
      // If the line only contains the marker, remove it
      if (currentLine.trim() === marker) {
        this.removeCurrentLineMarker();
      } else {
        // Continue the list
        const nextMarker = marker.match(/\d+/) ? 
          `${parseInt(marker) + 1}.` : marker;
        EditorUtils.insertTextAtCursor(this.editorElement, `\n${indent}${nextMarker} `);
      }
    }
  }

  /**
   * Handle editor scroll for synchronized scrolling
   */
  handleEditorScroll() {
    if (this.viewMode === 'split') {
      // Sync preview scroll with editor scroll
      const scrollPercentage = this.editorElement.scrollTop / 
        (this.editorElement.scrollHeight - this.editorElement.clientHeight);
      
      const previewScrollTop = scrollPercentage * 
        (this.previewElement.scrollHeight - this.previewElement.clientHeight);
      
      this.previewElement.scrollTop = previewScrollTop;
    }
  }

  /**
   * Handle focus events
   */
  handleFocus() {
    this.editorElement.classList.add('focused');
  }

  /**
   * Handle blur events
   */
  handleBlur() {
    this.editorElement.classList.remove('focused');
  }

  /**
   * Handle file drop
   */
  handleDrop(event) {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    
    files.forEach(file => {
      if (file.type.startsWith('text/') || file.name.endsWith('.md')) {
        window.fileManager?.loadFile(file);
      } else if (file.type.startsWith('image/')) {
        this.insertImageFromFile(file);
      }
    });
  }

  /**
   * Insert image from dropped file
   */
  insertImageFromFile(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const dataUrl = e.target.result;
      const altText = file.name.replace(/\.[^/.]+$/, '');
      EditorUtils.insertTextAtCursor(this.editorElement, `![${altText}](${dataUrl})`);
      this.handleInput();
    };
    reader.readAsDataURL(file);
  }

  /**
   * Get current line content
   */
  getCurrentLine() {
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);
    const textNode = range.startContainer;
    
    if (textNode.nodeType === Node.TEXT_NODE) {
      const text = textNode.textContent;
      const offset = range.startOffset;
      
      // Find line boundaries
      const beforeCursor = text.substring(0, offset);
      const afterCursor = text.substring(offset);
      
      const lineStart = beforeCursor.lastIndexOf('\n') + 1;
      const lineEnd = afterCursor.indexOf('\n');
      
      const lineEndPos = lineEnd === -1 ? text.length : offset + lineEnd;
      
      return text.substring(lineStart, lineEndPos);
    }
    
    return '';
  }

  /**
   * Remove current line marker (for empty list items)
   */
  removeCurrentLineMarker() {
    const currentLine = this.getCurrentLine();
    const markerMatch = currentLine.match(/^(\s*)([-*+]|\d+\.)\s*$/);
    
    if (markerMatch) {
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);
      
      // Select and delete the marker
      range.setStart(range.startContainer, range.startOffset - markerMatch[0].length);
      range.deleteContents();
    }
  }

  /**
   * Remove indentation from current line
   */
  removeIndentation() {
    const currentLine = this.getCurrentLine();
    if (currentLine.startsWith('  ')) {
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);
      
      // Remove two spaces from the beginning of the line
      const lineStart = range.startOffset - currentLine.length;
      range.setStart(range.startContainer, lineStart);
      range.setEnd(range.startContainer, lineStart + 2);
      range.deleteContents();
    }
  }

  /**
   * Set editor content
   */
  setContent(content) {
    this.currentContent = content || '';
    this.editorElement.textContent = this.currentContent;
    this.isModified = false;
    this.renderPreview().catch(console.error);
    this.updateStatistics();
    this.updateDocumentTitle();
  }

  /**
   * Get editor content
   */
  getContent() {
    return this.editorElement.textContent || '';
  }

  /**
   * Setup preview editing functionality
   */
  setupPreviewEditing() {
    // Make preview editable on double-click
    this.previewElement.addEventListener('dblclick', this.enablePreviewEditing.bind(this));

    // Handle preview input when in edit mode
    this.previewElement.addEventListener('input', this.handlePreviewInput.bind(this));

    // Handle preview blur to exit edit mode
    this.previewElement.addEventListener('blur', this.disablePreviewEditing.bind(this));

    // Handle escape key to exit edit mode
    this.previewElement.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.disablePreviewEditing();
      }
    });

    // Add visual indicator for editable preview
    this.previewElement.title = 'Double-click to edit directly in preview';
  }

  /**
   * Enable preview editing mode
   */
  enablePreviewEditing() {
    if (this.previewEditMode) return;

    this.previewEditMode = true;
    this.previewElement.contentEditable = true;
    this.previewElement.classList.add('preview-editing');
    this.previewElement.focus();

    // Store original HTML for potential restoration
    this.originalPreviewHTML = this.previewElement.innerHTML;

    // Show editing indicator
    this.showPreviewEditingIndicator();
  }

  /**
   * Disable preview editing mode
   */
  disablePreviewEditing() {
    if (!this.previewEditMode) return;

    this.previewEditMode = false;
    this.previewElement.contentEditable = false;
    this.previewElement.classList.remove('preview-editing');

    // Convert HTML back to markdown and update editor
    this.syncFromPreview();

    // Hide editing indicator
    this.hidePreviewEditingIndicator();
  }

  /**
   * Handle preview input events
   */
  handlePreviewInput() {
    if (!this.previewEditMode) return;

    this.isModified = true;
    this.updateDocumentTitle();
    this.debouncedSyncFromPreview();
  }

  /**
   * Sync content from preview back to editor
   */
  syncFromPreview() {
    if (!this.previewEditMode || this.isUpdatingFromPreview) return;

    try {
      // Get the text content from the preview (this strips HTML)
      const previewText = this.previewElement.innerText || this.previewElement.textContent || '';

      // Simple conversion back to markdown
      const markdown = this.convertHtmlToMarkdown(previewText);

      // Update editor without triggering preview update
      this.isUpdatingFromPreview = true;
      this.editorElement.textContent = markdown;
      this.currentContent = markdown;
      this.isUpdatingFromPreview = false;

      this.updateStatistics();
    } catch (error) {
      console.error('Error syncing from preview:', error);
    }
  }

  /**
   * Convert HTML content back to markdown (simplified)
   */
  convertHtmlToMarkdown(text) {
    // This is a simplified conversion - for a full implementation,
    // you'd want to use a proper HTML-to-Markdown converter
    let markdown = text;

    // Basic cleanup and formatting preservation
    markdown = markdown.replace(/\n\s*\n\s*\n/g, '\n\n'); // Remove excessive line breaks
    markdown = markdown.trim();

    return markdown;
  }

  /**
   * Show preview editing indicator
   */
  showPreviewEditingIndicator() {
    let indicator = document.getElementById('preview-edit-indicator');
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'preview-edit-indicator';
      indicator.innerHTML = `
        <i class="mdi mdi-pencil"></i>
        <span>Editing in preview - Press Escape to finish</span>
      `;
      document.body.appendChild(indicator);
    }
    indicator.style.display = 'flex';
  }

  /**
   * Hide preview editing indicator
   */
  hidePreviewEditingIndicator() {
    const indicator = document.getElementById('preview-edit-indicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  /**
   * Render preview
   */
  async renderPreview() {
    // Don't update preview if we're currently editing it
    if (this.previewEditMode || this.isUpdatingFromPreview) return;

    try {
      const content = this.getContent();

      if (!window.markdownRenderer) {
        console.error('Markdown renderer not available');
        this.previewElement.innerHTML = '<div>Markdown renderer not loaded</div>';
        return;
      }

      const html = await window.markdownRenderer.renderWithProcessing(content);
      this.previewElement.innerHTML = html;

      // Render math equations after setting HTML
      if (typeof renderMathInElement !== 'undefined') {
        try {
          renderMathInElement(this.previewElement, {
            delimiters: [
              {left: '$$', right: '$$', display: true},
              {left: '$', right: '$', display: false},
              {left: '\\(', right: '\\)', display: false},
              {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            errorColor: '#cc0000',
            ignoredTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'option']
          });
        } catch (mathError) {
          console.warn('KaTeX rendering error:', mathError);
        }
      }

      // Render Mermaid diagrams after setting HTML
      if (window.mermaidIntegration && window.mermaidIntegration.isAvailable()) {
        try {
          await window.mermaidIntegration.renderDiagrams(this.previewElement);
        } catch (mermaidError) {
          console.warn('Mermaid rendering error:', mermaidError);
        }
      }
    } catch (error) {
      console.error('Error in renderPreview:', error);
      this.previewElement.innerHTML = `<div style="color: red;">Error rendering preview: ${error.message}</div>`;
    }
  }

  /**
   * Update statistics
   */
  updateStatistics() {
    const content = this.getContent();
    const wordCount = EditorUtils.countWords(content);
    const charCount = EditorUtils.countCharacters(content);
    
    const wordCountElement = document.getElementById('word-count');
    const charCountElement = document.getElementById('char-count');
    
    if (wordCountElement) {
      wordCountElement.textContent = `${wordCount} word${wordCount !== 1 ? 's' : ''}`;
    }
    
    if (charCountElement) {
      charCountElement.textContent = `${charCount} character${charCount !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Update document title
   */
  updateDocumentTitle() {
    const modifiedIndicator = this.isModified ? '• ' : '';
    document.title = `${modifiedIndicator}${this.currentFileName} - MarkDown Editor`;
  }

  /**
   * Format selected text
   */
  formatText(prefix, suffix = '') {
    EditorUtils.wrapSelectedText(this.editorElement, prefix, suffix);
    this.handleInput();
    this.editorElement.focus();
  }

  /**
   * Insert link
   */
  insertLink() {
    const selectedText = EditorUtils.getSelectedText(this.editorElement);
    const url = EditorUtils.showPromptDialog('Enter URL:', 'https://');
    
    if (url) {
      const linkText = selectedText || 'Link text';
      EditorUtils.insertTextAtCursor(this.editorElement, `[${linkText}](${url})`);
      this.handleInput();
    }
    
    this.editorElement.focus();
  }

  /**
   * Insert image
   */
  insertImage() {
    const url = EditorUtils.showPromptDialog('Enter image URL:', 'https://');
    const alt = EditorUtils.showPromptDialog('Enter image description:', 'Image');
    
    if (url) {
      EditorUtils.insertTextAtCursor(this.editorElement, `![${alt || 'Image'}](${url})`);
      this.handleInput();
    }
    
    this.editorElement.focus();
  }

  /**
   * Toggle comment (add/remove > for blockquote)
   */
  toggleComment() {
    const selectedText = EditorUtils.getSelectedText(this.editorElement);
    if (selectedText) {
      const lines = selectedText.split('\n');
      const isCommented = lines.every(line => line.startsWith('> ') || line.trim() === '');

      const processedLines = lines.map(line => {
        if (isCommented) {
          return line.startsWith('> ') ? line.substring(2) : line;
        } else {
          return line.trim() ? `> ${line}` : line;
        }
      });

      EditorUtils.insertTextAtCursor(this.editorElement, processedLines.join('\n'));
      this.handleInput();
    }

    this.editorElement.focus();
  }

  /**
   * Insert math equation
   * @param {boolean} displayMode - Whether to insert display mode equation
   */
  insertEquation(displayMode = false) {
    if (window.katexIntegration && window.katexIntegration.isAvailable()) {
      window.katexIntegration.insertEquation(this.editorElement, displayMode);
      this.handleInput();
      this.editorElement.focus();
    } else {
      // Fallback: insert basic LaTeX syntax
      const delimiter = displayMode ? '$$' : '$';
      const placeholder = displayMode ? 'E = mc^2' : 'x^2';
      const equation = `${delimiter}${placeholder}${delimiter}`;
      EditorUtils.insertTextAtCursor(this.editorElement, equation);
      this.handleInput();
      this.editorElement.focus();
    }
  }

  /**
   * Set view mode
   */
  setViewMode(mode) {
    this.viewMode = mode;

    // Remove expanded state when changing view modes
    this.setPreviewExpanded(false);

    switch (mode) {
      case 'edit':
        this.editorElement.style.display = 'block';
        this.previewElement.style.display = 'none';
        break;
      case 'split':
        this.editorElement.style.display = 'block';
        this.previewElement.style.display = 'block';
        break;
      case 'preview':
        this.editorElement.style.display = 'none';
        this.previewElement.style.display = 'block';
        break;
    }

    // Update active button
    document.querySelectorAll('.view-mode-toggle button').forEach(btn => {
      btn.classList.remove('active');
      btn.setAttribute('aria-selected', 'false');
    });

    const activeButton = document.getElementById(`view-${mode}`);
    if (activeButton) {
      activeButton.classList.add('active');
      activeButton.setAttribute('aria-selected', 'true');
    }

    // Set body data attribute for CSS targeting
    document.body.setAttribute('data-view-mode', mode);
  }



  /**
   * Set filename
   */
  setFileName(filename) {
    this.currentFileName = filename;
    const fileNameElement = document.getElementById('file-name');
    if (fileNameElement) {
      fileNameElement.textContent = filename;
    }
    this.updateDocumentTitle();
  }

  /**
   * Mark as modified
   */
  markAsModified(modified = true) {
    this.isModified = modified;
    this.updateDocumentTitle();
  }

  /**
   * Focus editor
   */
  focus() {
    this.editorElement.focus();
  }
}

// Create global instance
window.editor = new Editor();
